#!/bin/bash

# Test script for SberRadar integration
# This script demonstrates how to test the SberRadar integration without Zabbix

echo "🚀 Testing SberRadar Integration"
echo "================================"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check if server is running
check_server() {
    if curl -s http://localhost:8080/health > /dev/null; then
        return 0
    else
        return 1
    fi
}

# Function to test endpoint
test_endpoint() {
    local endpoint=$1
    local method=$2
    local description=$3
    
    echo -e "\n${YELLOW}Testing: $description${NC}"
    echo "Endpoint: $method $endpoint"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" http://localhost:8080$endpoint)
    else
        response=$(curl -s -w "\n%{http_code}" -X $method http://localhost:8080$endpoint)
    fi
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ Success (HTTP $http_code)${NC}"
        echo "Response: $body" | jq . 2>/dev/null || echo "Response: $body"
    else
        echo -e "${RED}❌ Failed (HTTP $http_code)${NC}"
        echo "Response: $body"
    fi
}

# Check if server is running
echo "Checking if radar-integrator server is running..."
if check_server; then
    echo -e "${GREEN}✅ Server is running${NC}"
else
    echo -e "${RED}❌ Server is not running${NC}"
    echo "Please start the server first:"
    echo "  go run main.go"
    exit 1
fi

# Test health endpoint
test_endpoint "/health" "GET" "Health Check"

# Test single test metric
test_endpoint "/metrics/test" "POST" "Send Single Test Metric to SberRadar"

# Test full sync (multiple dummy metrics)
test_endpoint "/metrics/sync" "POST" "Send Multiple Dummy Metrics to SberRadar"

# Test validation with the updated payload
echo -e "\n${YELLOW}Testing: JSON Schema Validation${NC}"
echo "Endpoint: POST /validate/json"
if [ -f "payload2.json" ]; then
    response=$(curl -s -w "\n%{http_code}" -X POST http://localhost:8080/validate/json \
        -H "Content-Type: application/json" \
        -d @payload2.json)
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ Success (HTTP $http_code)${NC}"
        echo "Response: $body" | jq . 2>/dev/null || echo "Response: $body"
    else
        echo -e "${RED}❌ Failed (HTTP $http_code)${NC}"
        echo "Response: $body"
    fi
else
    echo -e "${RED}❌ payload2.json file not found${NC}"
fi

echo -e "\n${GREEN}🎉 Testing completed!${NC}"
echo ""
echo "📋 Available endpoints:"
echo "  GET  /health           - Health check"
echo "  GET  /ready            - Readiness check"
echo "  POST /metrics/test     - Send single test metric"
echo "  POST /metrics/sync     - Send multiple dummy metrics"
echo "  POST /validate/json    - Validate JSON against schema"
echo "  POST /validate/metric  - Validate Go struct"
echo "  POST /validate/metrics - Validate multiple metrics"
echo ""
echo "💡 To test with your own certificates:"
echo "  Update the SberRadar configuration in your .env file"
echo "  Make sure your certificates are accessible"
echo "  Check the logs for detailed error information"
