package models

import (
	"fmt"
	"strconv"
	"strings"
	"time"
)

// Helper functions for creating pointers to basic types
// These exist because <PERSON>'s pointer syntax is unnecessarily verbose
func StringPtr(s string) *string {
	return &s
}

func Float64Ptr(f float64) *float64 {
	return &f
}

func BoolPtr(b bool) *bool {
	return &b
}

func Int64Ptr(i int64) *int64 {
	return &i
}

// ZabbixMetric represents a metric from Zabbix
type ZabbixMetric struct {
	ItemID    string `json:"itemid"`
	Name      string `json:"name"`
	LastValue string `json:"lastvalue"`
	LastClock string `json:"lastclock"`
}

// SberRadarMetric represents a transformed metric for SberRadar API
type SberRadarMetric struct {
	// Core metric fields
	MetricValue       float64  `json:"metric_value"`
	BaselineValue     *float64 `json:"baseline_value"`
	MetricTimestamp   int64    `json:"metric_timestamp"`
	MetricActualDate  int64    `json:"metric_actual_date"`
	Version           string   `json:"version"`
	ITServiceCI       string   `json:"it_service_ci"`
	ObjectCI          *string  `json:"object_ci"`
	MetricName        string   `json:"metric_name"`
	MetricDescription string   `json:"metric_description"`
	MetricType        string   `json:"metric_type"`
	MetricUnit        string   `json:"metric_unit"`
	MetricPeriodSec   int      `json:"metric_period_sec"`

	// Threshold fields
	ThresholdNormalMin      *float64 `json:"threshold_normal_min,omitempty"`
	ThresholdNormalMax      *float64 `json:"threshold_normal_max,omitempty"`
	ThresholdWarningMin     *float64 `json:"threshold_warning_min,omitempty"`
	ThresholdWarningMax     *float64 `json:"threshold_warning_max,omitempty"`
	ThresholdHighMin        *float64 `json:"threshold_high_min,omitempty"`
	ThresholdHighMax        *float64 `json:"threshold_high_max,omitempty"`
	ThresholdCriticalMin    *float64 `json:"threshold_critical_min,omitempty"`
	ThresholdCriticalMax    *float64 `json:"threshold_critical_max,omitempty"`
	ThresholdWideRangingMin *float64 `json:"threshold_wide_ranging_min,omitempty"`
	ThresholdWideRangingMax *float64 `json:"threshold_wide_ranging_max,omitempty"`

	// Baseline deviation fields
	BaselineNormalMin      *float64 `json:"baseline_normal_min,omitempty"`
	BaselineNormalMax      *float64 `json:"baseline_normal_max,omitempty"`
	BaselineWarningMin     *float64 `json:"baseline_warning_min,omitempty"`
	BaselineWarningMax     *float64 `json:"baseline_warning_max,omitempty"`
	BaselineHighMin        *float64 `json:"baseline_high_min,omitempty"`
	BaselineHighMax        *float64 `json:"baseline_high_max,omitempty"`
	BaselineCriticalMin    *float64 `json:"baseline_critical_min,omitempty"`
	BaselineCriticalMax    *float64 `json:"baseline_critical_max,omitempty"`
	BaselineWideRangingMin *float64 `json:"baseline_wide_ranging_min,omitempty"`
	BaselineWideRangingMax *float64 `json:"baseline_wide_ranging_max,omitempty"`

	// Additional fields
	CombinationSelector *string `json:"combination_selector,omitempty"`
	IsPercent           *bool   `json:"is_percent,omitempty"`
	ThresholdTimestamp  *int64  `json:"threshold_timestamp,omitempty"`
}

// SberRadarPayload represents the payload sent to SberRadar
type SberRadarPayload struct {
	Source    string             `json:"source"`
	Timestamp int64              `json:"timestamp"`
	Metrics   []*SberRadarMetric `json:"metrics"`
}

// SyncResult represents the result of a metrics synchronization
type SyncResult struct {
	MetricsSent int
	ErrorCount  int
	Errors      []error
}

// TransformZabbixToSberRadar transforms Zabbix metrics to SberRadar format
func TransformZabbixToSberRadar(zabbixMetrics []*ZabbixMetric) ([]*SberRadarMetric, error) {
	sberRadarMetrics := make([]*SberRadarMetric, 0, len(zabbixMetrics))

	for _, zm := range zabbixMetrics {
		// Parse timestamp from Zabbix format
		timestamp := time.Now().Unix() // Default to current time
		// TODO: Parse zm.LastClock properly

		// Parse metric value
		metricValue, err := parseMetricValueAsFloat(zm.LastValue)
		if err != nil {
			// Skip metrics with invalid values
			continue
		}

		// Transform metric name
		metricName := transformMetricName(zm.Name)

		// Create baseline value (can be null)
		var baselineValue *float64
		if metricValue > 0 {
			baseline := metricValue * 0.8 // Example: 80% of current value as baseline
			baselineValue = &baseline
		}

		// Determine metric type based on name
		metricType := determineMetricType(zm.Name)

		// Default object CI (can be null)
		var objectCI *string
		defaultObjectCI := "CI87654321" // This should come from configuration
		objectCI = &defaultObjectCI

		sberRadarMetric := &SberRadarMetric{
			MetricValue:       metricValue,
			BaselineValue:     baselineValue,
			MetricTimestamp:   timestamp,
			MetricActualDate:  timestamp,
			Version:           "1.0.0",
			ITServiceCI:       "CI12345678", // This should come from configuration
			ObjectCI:          objectCI,
			MetricName:        metricName,
			MetricDescription: generateMetricDescription(zm.Name),
			MetricType:        metricType,
			MetricUnit:        determineMetricUnit(zm.Name),
			MetricPeriodSec:   300, // 5 minutes default
		}

		sberRadarMetrics = append(sberRadarMetrics, sberRadarMetric)
	}

	return sberRadarMetrics, nil
}

// transformMetricName transforms Zabbix metric name to SberRadar format
// This is a placeholder implementation - customize based on your requirements
func transformMetricName(zabbixName string) string {
	// Example transformation: replace spaces with underscores, convert to lowercase
	// TODO: Implement actual transformation logic based on requirements
	return "radar." + zabbixName
}

// parseMetricValueAsFloat parses Zabbix metric value as float64
func parseMetricValueAsFloat(value string) (float64, error) {
	if value == "" {
		return 0, fmt.Errorf("empty value")
	}

	// Try to parse as float
	if floatVal, err := strconv.ParseFloat(value, 64); err == nil {
		return floatVal, nil
	}

	// If it's not a number, return error
	return 0, fmt.Errorf("cannot parse '%s' as float", value)
}

// determineMetricType determines SberRadar metric type based on Zabbix metric name
func determineMetricType(zabbixName string) string {
	name := strings.ToLower(zabbixName)

	switch {
	case strings.Contains(name, "cpu") || strings.Contains(name, "memory") || strings.Contains(name, "disk"):
		return "SATURATION"
	case strings.Contains(name, "response") || strings.Contains(name, "latency") || strings.Contains(name, "time"):
		return "LATENCY"
	case strings.Contains(name, "availability") || strings.Contains(name, "uptime"):
		return "AVAILABILITY"
	case strings.Contains(name, "error") || strings.Contains(name, "fail"):
		return "REQ_IN_ERROR"
	case strings.Contains(name, "success") || strings.Contains(name, "ok"):
		return "REQ_IN_SUCCESS"
	default:
		return "OTHER"
	}
}

// generateMetricDescription generates a description for the metric
func generateMetricDescription(zabbixName string) string {
	// Simple mapping of common Zabbix metrics to descriptions
	descriptions := map[string]string{
		"system.cpu.util": "Процент использования CPU",
		"vm.memory.util":  "Процент использования памяти",
		"net.if.in":       "Входящий сетевой трафик",
		"net.if.out":      "Исходящий сетевой трафик",
		"system.load":     "Средняя нагрузка системы",
		"vfs.fs.size":     "Использование дискового пространства",
		"system.uptime":   "Время работы системы",
	}

	for pattern, desc := range descriptions {
		if strings.Contains(zabbixName, pattern) {
			return desc
		}
	}

	// Default description
	return fmt.Sprintf("Метрика %s из системы мониторинга Zabbix", zabbixName)
}

// determineMetricUnit determines the unit of measurement
func determineMetricUnit(zabbixName string) string {
	name := strings.ToLower(zabbixName)

	switch {
	case strings.Contains(name, "util") || strings.Contains(name, "percent"):
		return "процент"
	case strings.Contains(name, "bytes") || strings.Contains(name, "size"):
		return "байт"
	case strings.Contains(name, "time") || strings.Contains(name, "uptime"):
		return "секунда"
	case strings.Contains(name, "load"):
		return "единица"
	case strings.Contains(name, "count") || strings.Contains(name, "num"):
		return "штука"
	default:
		return "единица"
	}
}
