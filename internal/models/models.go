package models

import (
	"fmt"
	"strconv"
	"strings"
	"time"
)

// ZabbixMetric represents a metric from Zabbix
type ZabbixMetric struct {
	ItemID    string `json:"itemid"`
	Name      string `json:"name"`
	LastValue string `json:"lastvalue"`
	LastClock string `json:"lastclock"`
}

// SberRadarMetric represents a transformed metric for SberRadar API
type SberRadarMetric struct {
	MetricValue       float64  `json:"metric_value"`
	BaselineValue     *float64 `json:"baseline_value"`
	MetricTimestamp   int64    `json:"metric_timestamp"`
	MetricActualDate  int64    `json:"metric_actual_date"`
	Version           string   `json:"version"`
	ITServiceCI       string   `json:"it_service_ci"`
	ObjectCI          *string  `json:"object_ci"`
	MetricName        string   `json:"metric_name"`
	MetricDescription string   `json:"metric_description"`
	MetricType        string   `json:"metric_type"`
	MetricUnit        string   `json:"metric_unit"`
	MetricPeriodSec   int      `json:"metric_period_sec"`
}

// SberRadarPayload represents the payload sent to SberRadar
type SberRadarPayload struct {
	Source    string             `json:"source"`
	Timestamp int64              `json:"timestamp"`
	Metrics   []*SberRadarMetric `json:"metrics"`
}

// SyncResult represents the result of a metrics synchronization
type SyncResult struct {
	MetricsSent int
	ErrorCount  int
	Errors      []error
}

// TransformZabbixToSberRadar transforms Zabbix metrics to SberRadar format
func TransformZabbixToSberRadar(zabbixMetrics []*ZabbixMetric) ([]*SberRadarMetric, error) {
	sberRadarMetrics := make([]*SberRadarMetric, 0, len(zabbixMetrics))

	for _, zm := range zabbixMetrics {
		// Parse timestamp from Zabbix format
		timestamp := time.Now().Unix() // Default to current time
		// TODO: Parse zm.LastClock properly

		// Parse metric value
		metricValue, err := parseMetricValueAsFloat(zm.LastValue)
		if err != nil {
			// Skip metrics with invalid values
			continue
		}

		// Transform metric name
		metricName := transformMetricName(zm.Name)

		// Create baseline value (can be null)
		var baselineValue *float64
		if metricValue > 0 {
			baseline := metricValue * 0.8 // Example: 80% of current value as baseline
			baselineValue = &baseline
		}

		// Determine metric type based on name
		metricType := determineMetricType(zm.Name)

		// Default object CI (can be null)
		var objectCI *string
		defaultObjectCI := "CI87654321" // This should come from configuration
		objectCI = &defaultObjectCI

		sberRadarMetric := &SberRadarMetric{
			MetricValue:       metricValue,
			BaselineValue:     baselineValue,
			MetricTimestamp:   timestamp,
			MetricActualDate:  timestamp,
			Version:           "1.0.0",
			ITServiceCI:       "CI12345678", // This should come from configuration
			ObjectCI:          objectCI,
			MetricName:        metricName,
			MetricDescription: generateMetricDescription(zm.Name),
			MetricType:        metricType,
			MetricUnit:        determineMetricUnit(zm.Name),
			MetricPeriodSec:   300, // 5 minutes default
		}

		sberRadarMetrics = append(sberRadarMetrics, sberRadarMetric)
	}

	return sberRadarMetrics, nil
}

// transformMetricName transforms Zabbix metric name to SberRadar format
// This is a placeholder implementation - customize based on your requirements
func transformMetricName(zabbixName string) string {
	// Example transformation: replace spaces with underscores, convert to lowercase
	// TODO: Implement actual transformation logic based on requirements
	return "radar." + zabbixName
}

// parseMetricValueAsFloat parses Zabbix metric value as float64
func parseMetricValueAsFloat(value string) (float64, error) {
	if value == "" {
		return 0, fmt.Errorf("empty value")
	}

	// Try to parse as float
	if floatVal, err := strconv.ParseFloat(value, 64); err == nil {
		return floatVal, nil
	}

	// If it's not a number, return error
	return 0, fmt.Errorf("cannot parse '%s' as float", value)
}

// determineMetricType determines SberRadar metric type based on Zabbix metric name
func determineMetricType(zabbixName string) string {
	name := strings.ToLower(zabbixName)

	switch {
	case strings.Contains(name, "cpu") || strings.Contains(name, "memory") || strings.Contains(name, "disk"):
		return "SATURATION"
	case strings.Contains(name, "response") || strings.Contains(name, "latency") || strings.Contains(name, "time"):
		return "LATENCY"
	case strings.Contains(name, "availability") || strings.Contains(name, "uptime"):
		return "AVAILABILITY"
	case strings.Contains(name, "error") || strings.Contains(name, "fail"):
		return "REQ_IN_ERROR"
	case strings.Contains(name, "success") || strings.Contains(name, "ok"):
		return "REQ_IN_SUCCESS"
	default:
		return "OTHER"
	}
}

// generateMetricDescription generates a description for the metric
func generateMetricDescription(zabbixName string) string {
	// Simple mapping of common Zabbix metrics to descriptions
	descriptions := map[string]string{
		"system.cpu.util": "Процент использования CPU",
		"vm.memory.util":  "Процент использования памяти",
		"net.if.in":       "Входящий сетевой трафик",
		"net.if.out":      "Исходящий сетевой трафик",
		"system.load":     "Средняя нагрузка системы",
		"vfs.fs.size":     "Использование дискового пространства",
		"system.uptime":   "Время работы системы",
	}

	for pattern, desc := range descriptions {
		if strings.Contains(zabbixName, pattern) {
			return desc
		}
	}

	// Default description
	return fmt.Sprintf("Метрика %s из системы мониторинга Zabbix", zabbixName)
}

// determineMetricUnit determines the unit of measurement
func determineMetricUnit(zabbixName string) string {
	name := strings.ToLower(zabbixName)

	switch {
	case strings.Contains(name, "util") || strings.Contains(name, "percent"):
		return "процент"
	case strings.Contains(name, "bytes") || strings.Contains(name, "size"):
		return "байт"
	case strings.Contains(name, "time") || strings.Contains(name, "uptime"):
		return "секунда"
	case strings.Contains(name, "load"):
		return "единица"
	case strings.Contains(name, "count") || strings.Contains(name, "num"):
		return "штука"
	default:
		return "единица"
	}
}
