package handlers

import (
	"encoding/json"
	"net/http"

	"radar-integrator/internal/services"

	"github.com/sirupsen/logrus"
)

// MetricsHandler handles metrics-related endpoints
type MetricsHandler struct {
	integrationService *services.IntegrationService
	validationService  *services.ValidationService
	logger             *logrus.Logger
}

// NewMetricsHandler creates a new metrics handler
func NewMetricsHandler(integrationService *services.IntegrationService, validationService *services.ValidationService, logger *logrus.Logger) *MetricsHandler {
	return &MetricsHandler{
		integrationService: integrationService,
		validationService:  validationService,
		logger:             logger,
	}
}

// SyncMetricsResponse represents the response for metrics sync
type SyncMetricsResponse struct {
	Status      string `json:"status"`
	Message     string `json:"message"`
	MetricsSent int    `json:"metrics_sent"`
	ErrorCount  int    `json:"error_count"`
}

// SyncMetrics handles the /metrics/sync endpoint
func (h *MetricsHandler) SyncMetrics(w http.ResponseWriter, r *http.Request) {
	h.logger.Info("Starting metrics synchronization")

	result, err := h.integrationService.SyncMetrics(r.Context())
	if err != nil {
		h.logger.WithError(err).Error("Failed to sync metrics")

		response := SyncMetricsResponse{
			Status:  "error",
			Message: err.Error(),
		}

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(response)
		return
	}

	response := SyncMetricsResponse{
		Status:      "success",
		Message:     "Metrics synchronized successfully",
		MetricsSent: result.MetricsSent,
		ErrorCount:  result.ErrorCount,
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	if err := json.NewEncoder(w).Encode(response); err != nil {
		h.logger.WithError(err).Error("Failed to encode sync response")
	}

	h.logger.WithFields(logrus.Fields{
		"metrics_sent": result.MetricsSent,
		"error_count":  result.ErrorCount,
	}).Info("Metrics synchronization completed")
}
