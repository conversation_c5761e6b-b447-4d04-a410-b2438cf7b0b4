package handlers

import (
	"encoding/json"
	"io"
	"net/http"

	"radar-integrator/internal/models"
	"radar-integrator/internal/services"

	"github.com/sirupsen/logrus"
)

// ValidationHandler handles validation-related endpoints
type ValidationHandler struct {
	validationService *services.ValidationService
	logger            *logrus.Logger
}

// NewValidationHandler creates a new validation handler
func NewValidationHandler(validationService *services.ValidationService, logger *logrus.Logger) *ValidationHandler {
	return &ValidationHandler{
		validationService: validationService,
		logger:            logger,
	}
}

// ValidateMetricRequest represents the request for metric validation
type ValidateMetricRequest struct {
	Metric *models.SberRadarMetric `json:"metric"`
}

// ValidateMetricsRequest represents the request for multiple metrics validation
type ValidateMetricsRequest struct {
	Metrics []*models.SberRadarMetric `json:"metrics"`
}

// ValidateMetricResponse represents the response for metric validation
type ValidateMetricResponse struct {
	Status string                        `json:"status"`
	Valid  bool                          `json:"valid"`
	Result *services.ValidationResult    `json:"result,omitempty"`
	Error  string                        `json:"error,omitempty"`
}

// ValidateMetricsResponse represents the response for multiple metrics validation
type ValidateMetricsResponse struct {
	Status  string                         `json:"status"`
	Results []*services.ValidationResult   `json:"results,omitempty"`
	Summary map[string]interface{}         `json:"summary,omitempty"`
	Error   string                         `json:"error,omitempty"`
}

// ValidateMetric handles the /validate/metric endpoint
func (h *ValidationHandler) ValidateMetric(w http.ResponseWriter, r *http.Request) {
	h.logger.Info("Validating single metric")

	// Read request body
	body, err := io.ReadAll(r.Body)
	if err != nil {
		h.logger.WithError(err).Error("Failed to read request body")
		h.sendErrorResponse(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	// Try to parse as a metric directly first
	var metric models.SberRadarMetric
	if err := json.Unmarshal(body, &metric); err != nil {
		// If that fails, try to parse as a request wrapper
		var request ValidateMetricRequest
		if err := json.Unmarshal(body, &request); err != nil {
			h.logger.WithError(err).Error("Failed to parse request")
			h.sendErrorResponse(w, "Invalid JSON format", http.StatusBadRequest)
			return
		}
		if request.Metric == nil {
			h.sendErrorResponse(w, "Missing metric in request", http.StatusBadRequest)
			return
		}
		metric = *request.Metric
	}

	// Validate the metric
	result, err := h.validationService.ValidateMetric(&metric)
	if err != nil {
		h.logger.WithError(err).Error("Failed to validate metric")
		h.sendErrorResponse(w, "Validation failed", http.StatusInternalServerError)
		return
	}

	// Send response
	response := ValidateMetricResponse{
		Status: "success",
		Valid:  result.Valid,
		Result: result,
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	if err := json.NewEncoder(w).Encode(response); err != nil {
		h.logger.WithError(err).Error("Failed to encode validation response")
	}

	h.logger.WithFields(logrus.Fields{
		"valid":        result.Valid,
		"errors_count": len(result.Errors),
	}).Info("Metric validation completed")
}

// ValidateMetrics handles the /validate/metrics endpoint
func (h *ValidationHandler) ValidateMetrics(w http.ResponseWriter, r *http.Request) {
	h.logger.Info("Validating multiple metrics")

	// Parse request
	var request ValidateMetricsRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		h.logger.WithError(err).Error("Failed to parse request")
		h.sendErrorResponse(w, "Invalid JSON format", http.StatusBadRequest)
		return
	}

	if len(request.Metrics) == 0 {
		h.sendErrorResponse(w, "No metrics provided", http.StatusBadRequest)
		return
	}

	// Validate metrics
	results, err := h.validationService.ValidateMetrics(request.Metrics)
	if err != nil {
		h.logger.WithError(err).Error("Failed to validate metrics")
		h.sendErrorResponse(w, "Validation failed", http.StatusInternalServerError)
		return
	}

	// Get summary
	summary := h.validationService.GetValidationSummary(results)

	// Send response
	response := ValidateMetricsResponse{
		Status:  "success",
		Results: results,
		Summary: summary,
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	if err := json.NewEncoder(w).Encode(response); err != nil {
		h.logger.WithError(err).Error("Failed to encode validation response")
	}

	h.logger.WithFields(logrus.Fields{
		"total_metrics":   summary["total_metrics"],
		"valid_metrics":   summary["valid_metrics"],
		"invalid_metrics": summary["invalid_metrics"],
	}).Info("Metrics validation completed")
}

// ValidateRawJSON handles the /validate/json endpoint for raw JSON validation
func (h *ValidationHandler) ValidateRawJSON(w http.ResponseWriter, r *http.Request) {
	h.logger.Info("Validating raw JSON")

	// Read request body
	body, err := io.ReadAll(r.Body)
	if err != nil {
		h.logger.WithError(err).Error("Failed to read request body")
		h.sendErrorResponse(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	// Validate the JSON directly
	result, err := h.validationService.ValidateMetricJSON(body)
	if err != nil {
		h.logger.WithError(err).Error("Failed to validate JSON")
		h.sendErrorResponse(w, "Validation failed", http.StatusInternalServerError)
		return
	}

	// Send response
	response := ValidateMetricResponse{
		Status: "success",
		Valid:  result.Valid,
		Result: result,
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	if err := json.NewEncoder(w).Encode(response); err != nil {
		h.logger.WithError(err).Error("Failed to encode validation response")
	}

	h.logger.WithFields(logrus.Fields{
		"valid":        result.Valid,
		"errors_count": len(result.Errors),
	}).Info("Raw JSON validation completed")
}

// sendErrorResponse sends an error response
func (h *ValidationHandler) sendErrorResponse(w http.ResponseWriter, message string, statusCode int) {
	response := ValidateMetricResponse{
		Status: "error",
		Valid:  false,
		Error:  message,
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	if err := json.NewEncoder(w).Encode(response); err != nil {
		h.logger.WithError(err).Error("Failed to encode error response")
	}
}
