package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"radar-integrator/internal/models"
	"radar-integrator/internal/services"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestValidationHandler(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel) // Reduce noise in tests

	validationService, err := services.NewValidationService(logger)
	require.NoError(t, err)

	handler := NewValidationHandler(validationService, logger)

	t.Run("ValidateMetric_ValidData", func(t *testing.T) {
		metric := models.SberRadarMetric{
			MetricValue:       85.5,
			BaselineValue:     &[]float64{80.0}[0],
			MetricTimestamp:   1725369600,
			MetricActualDate:  1725369600,
			Version:           "1.0.0",
			ITServiceCI:       "**********",
			ObjectCI:          &[]string{"**********"}[0],
			MetricName:        "CPU_Utilization_Test",
			MetricDescription: "Тестовая метрика использования CPU",
			MetricType:        "SATURATION",
			MetricUnit:        "процент",
			MetricPeriodSec:   300,
		}

		jsonData, err := json.Marshal(metric)
		require.NoError(t, err)

		req := httptest.NewRequest("POST", "/validate/metric", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		handler.ValidateMetric(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response ValidateMetricResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, "success", response.Status)
		assert.True(t, response.Valid)
		assert.NotNil(t, response.Result)
		assert.Empty(t, response.Result.Errors)
	})

	t.Run("ValidateMetric_InvalidData", func(t *testing.T) {
		metric := models.SberRadarMetric{
			MetricValue:      85.5,
			BaselineValue:    &[]float64{80.0}[0],
			MetricTimestamp:  1725369600,
			MetricActualDate: 1725369600,
			// Missing Version field
			ITServiceCI:       "**********",
			ObjectCI:          &[]string{"**********"}[0],
			MetricName:        "CPU_Utilization_Test",
			MetricDescription: "Тестовая метрика использования CPU",
			MetricType:        "SATURATION",
			MetricUnit:        "процент",
			MetricPeriodSec:   300,
		}

		jsonData, err := json.Marshal(metric)
		require.NoError(t, err)

		req := httptest.NewRequest("POST", "/validate/metric", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		handler.ValidateMetric(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response ValidateMetricResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, "success", response.Status)
		assert.False(t, response.Valid)
		assert.NotNil(t, response.Result)
		assert.NotEmpty(t, response.Result.Errors)
	})

	t.Run("ValidateRawJSON_ValidData", func(t *testing.T) {
		jsonData := `{
			"metric_value": 85.5,
			"baseline_value": 80.0,
			"metric_timestamp": 1725369600,
			"metric_actual_date": 1725369600,
			"version": "1.0.0",
			"it_service_ci": "**********",
			"object_ci": "**********",
			"metric_name": "CPU_Utilization_Test",
			"metric_description": "Тестовая метрика использования CPU",
			"metric_type": "SATURATION",
			"metric_unit": "процент",
			"metric_period_sec": 300
		}`

		req := httptest.NewRequest("POST", "/validate/json", bytes.NewBufferString(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		handler.ValidateRawJSON(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response ValidateMetricResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, "success", response.Status)
		assert.True(t, response.Valid)
		assert.NotNil(t, response.Result)
		assert.Empty(t, response.Result.Errors)
	})

	t.Run("ValidateRawJSON_ValidDataWithThresholds", func(t *testing.T) {
		jsonData := `{
			"metric_value": 85.5,
			"baseline_value": 80.0,
			"metric_timestamp": 1725369600,
			"metric_actual_date": 1725369600,
			"version": "1.0.0",
			"it_service_ci": "**********",
			"object_ci": "**********",
			"metric_name": "CPU_Utilization_Test",
			"metric_description": "Тестовая метрика использования CPU",
			"metric_type": "SATURATION",
			"metric_unit": "процент",
			"metric_period_sec": 300,
			"threshold_normal_min": 0.0,
			"threshold_normal_max": 70.0,
			"threshold_warning_min": 70.0,
			"threshold_warning_max": 85.0,
			"combination_selector": "worst",
			"is_percent": true,
			"threshold_timestamp": 1725369600
		}`

		req := httptest.NewRequest("POST", "/validate/json", bytes.NewBufferString(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		handler.ValidateRawJSON(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response ValidateMetricResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, "success", response.Status)
		assert.True(t, response.Valid)
		assert.NotNil(t, response.Result)
		assert.Empty(t, response.Result.Errors)
	})

	t.Run("ValidateRawJSON_InvalidData", func(t *testing.T) {
		jsonData := `{
			"metric_value": 85.5,
			"baseline_value": 80.0,
			"metric_timestamp": 1725369600,
			"metric_actual_date": 1725369600,
			"it_service_ci": "INVALID123",
			"object_ci": "**********",
			"metric_name": "CPU_Utilization_Test",
			"metric_description": "Тестовая метрика использования CPU",
			"metric_type": "SATURATION",
			"metric_unit": "процент",
			"metric_period_sec": 300
		}`

		req := httptest.NewRequest("POST", "/validate/json", bytes.NewBufferString(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		handler.ValidateRawJSON(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response ValidateMetricResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, "success", response.Status)
		assert.False(t, response.Valid)
		assert.NotNil(t, response.Result)
		assert.NotEmpty(t, response.Result.Errors)
	})

	t.Run("ValidateMetrics_MultipleMetrics", func(t *testing.T) {
		request := ValidateMetricsRequest{
			Metrics: []*models.SberRadarMetric{
				{
					MetricValue:       85.5,
					BaselineValue:     &[]float64{80.0}[0],
					MetricTimestamp:   1725369600,
					MetricActualDate:  1725369600,
					Version:           "1.0.0",
					ITServiceCI:       "**********",
					ObjectCI:          &[]string{"**********"}[0],
					MetricName:        "CPU_Utilization_Test",
					MetricDescription: "Тестовая метрика использования CPU",
					MetricType:        "SATURATION",
					MetricUnit:        "процент",
					MetricPeriodSec:   300,
				},
				{
					MetricValue:      75.0,
					BaselineValue:    &[]float64{70.0}[0],
					MetricTimestamp:  1725369600,
					MetricActualDate: 1725369600,
					// Missing Version field
					ITServiceCI:       "**********",
					ObjectCI:          &[]string{"**********"}[0],
					MetricName:        "Memory_Utilization_Test",
					MetricDescription: "Тестовая метрика использования памяти",
					MetricType:        "SATURATION",
					MetricUnit:        "процент",
					MetricPeriodSec:   300,
				},
			},
		}

		jsonData, err := json.Marshal(request)
		require.NoError(t, err)

		req := httptest.NewRequest("POST", "/validate/metrics", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		handler.ValidateMetrics(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response ValidateMetricsResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, "success", response.Status)
		assert.Len(t, response.Results, 2)
		assert.True(t, response.Results[0].Valid)
		assert.False(t, response.Results[1].Valid)
		assert.NotNil(t, response.Summary)
		assert.Equal(t, 2, response.Summary["total_metrics"])
		assert.Equal(t, 1, response.Summary["valid_metrics"])
		assert.Equal(t, 1, response.Summary["invalid_metrics"])
	})
}
