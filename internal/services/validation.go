package services

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"radar-integrator/internal/models"

	"github.com/sirupsen/logrus"
	"github.com/xeipuuv/gojsonschema"
)

// We'll load the schema from file system instead of embedding for now

// ValidationService handles JSON Schema validation for metrics
type ValidationService struct {
	schema gojsonschema.JSONLoader
	logger *logrus.Logger
}

// ValidationError represents a validation error with details
type ValidationError struct {
	Field       string      `json:"field"`
	Value       interface{} `json:"value"`
	Description string      `json:"description"`
	Type        string      `json:"type"`
}

// ValidationResult represents the result of validation
type ValidationResult struct {
	Valid  bool              `json:"valid"`
	Errors []ValidationError `json:"errors,omitempty"`
}

// NewValidationService creates a new validation service
func NewValidationService(logger *logrus.Logger) (*ValidationService, error) {
	// Load schema from file system
	schemaPath := filepath.Join("schemas", "sberradar-metric-schema.json")
	schemaData, err := os.ReadFile(schemaPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read schema file %s: %w", schemaPath, err)
	}

	// Create schema loader
	schemaLoader := gojsonschema.NewBytesLoader(schemaData)

	return &ValidationService{
		schema: schemaLoader,
		logger: logger,
	}, nil
}

// ValidateMetric validates a single SberRadar metric against the JSON schema
func (v *ValidationService) ValidateMetric(metric *models.SberRadarMetric) (*ValidationResult, error) {
	// Convert metric to JSON
	metricJSON, err := json.Marshal(metric)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal metric: %w", err)
	}

	return v.ValidateMetricJSON(metricJSON)
}

// ValidateMetricJSON validates JSON bytes against the schema
func (v *ValidationService) ValidateMetricJSON(metricJSON []byte) (*ValidationResult, error) {
	// Create document loader
	documentLoader := gojsonschema.NewBytesLoader(metricJSON)

	// Validate
	result, err := gojsonschema.Validate(v.schema, documentLoader)
	if err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Convert result
	validationResult := &ValidationResult{
		Valid:  result.Valid(),
		Errors: make([]ValidationError, 0),
	}

	if !result.Valid() {
		for _, err := range result.Errors() {
			validationError := ValidationError{
				Field:       err.Field(),
				Value:       err.Value(),
				Description: err.Description(),
				Type:        err.Type(),
			}
			validationResult.Errors = append(validationResult.Errors, validationError)
		}

		v.logger.WithFields(logrus.Fields{
			"errors_count": len(validationResult.Errors),
			"errors":       validationResult.Errors,
		}).Warn("Metric validation failed")
	}

	return validationResult, nil
}

// ValidateMetrics validates multiple metrics
func (v *ValidationService) ValidateMetrics(metrics []*models.SberRadarMetric) ([]*ValidationResult, error) {
	results := make([]*ValidationResult, len(metrics))

	for i, metric := range metrics {
		result, err := v.ValidateMetric(metric)
		if err != nil {
			return nil, fmt.Errorf("failed to validate metric %d: %w", i, err)
		}
		results[i] = result
	}

	return results, nil
}

// GetValidationSummary returns a summary of validation results
func (v *ValidationService) GetValidationSummary(results []*ValidationResult) map[string]interface{} {
	totalMetrics := len(results)
	validMetrics := 0
	invalidMetrics := 0
	totalErrors := 0
	errorsByType := make(map[string]int)
	errorsByField := make(map[string]int)

	for _, result := range results {
		if result.Valid {
			validMetrics++
		} else {
			invalidMetrics++
			totalErrors += len(result.Errors)

			for _, err := range result.Errors {
				errorsByType[err.Type]++
				// Clean field name (remove array indices and dots)
				fieldName := strings.Split(err.Field, ".")[0]
				fieldName = strings.Split(fieldName, "[")[0]
				errorsByField[fieldName]++
			}
		}
	}

	return map[string]interface{}{
		"total_metrics":   totalMetrics,
		"valid_metrics":   validMetrics,
		"invalid_metrics": invalidMetrics,
		"total_errors":    totalErrors,
		"errors_by_type":  errorsByType,
		"errors_by_field": errorsByField,
	}
}

// IsValidMetric is a convenience method to check if a metric is valid
func (v *ValidationService) IsValidMetric(metric *models.SberRadarMetric) bool {
	result, err := v.ValidateMetric(metric)
	if err != nil {
		v.logger.WithError(err).Error("Failed to validate metric")
		return false
	}
	return result.Valid
}
