package services

import (
	"testing"
	"time"

	"radar-integrator/internal/models"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestValidationService(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel) // Reduce noise in tests

	validationService, err := NewValidationService(logger)
	require.NoError(t, err, "Failed to create validation service")

	t.Run("ValidMetric", func(t *testing.T) {
		metric := &models.SberRadarMetric{
			MetricValue:       85.5,
			BaselineValue:     &[]float64{80.0}[0],
			MetricTimestamp:   time.Now().Unix(),
			MetricActualDate:  time.Now().Unix(),
			Version:           "1.0.0",
			ITServiceCI:       "CI12345678",
			ObjectCI:          &[]string{"CI87654321"}[0],
			MetricName:        "CPU_Utilization_Test",
			MetricDescription: "Тестовая метрика использования CPU",
			MetricType:        "SATURATION",
			MetricUnit:        "процент",
			MetricPeriodSec:   300,
		}

		result, err := validationService.ValidateMetric(metric)
		require.NoError(t, err)
		assert.True(t, result.Valid, "Valid metric should pass validation")
		assert.Empty(t, result.Errors, "Valid metric should have no errors")
	})

	t.Run("InvalidMetric_MissingRequiredField", func(t *testing.T) {
		metric := &models.SberRadarMetric{
			MetricValue:      85.5,
			BaselineValue:    &[]float64{80.0}[0],
			MetricTimestamp:  time.Now().Unix(),
			MetricActualDate: time.Now().Unix(),
			// Missing Version field
			ITServiceCI:       "CI12345678",
			ObjectCI:          &[]string{"CI87654321"}[0],
			MetricName:        "CPU_Utilization_Test",
			MetricDescription: "Тестовая метрика использования CPU",
			MetricType:        "SATURATION",
			MetricUnit:        "процент",
			MetricPeriodSec:   300,
		}

		result, err := validationService.ValidateMetric(metric)
		require.NoError(t, err)
		assert.False(t, result.Valid, "Invalid metric should fail validation")
		assert.NotEmpty(t, result.Errors, "Invalid metric should have errors")
		
		// Check that the error is about the missing version field
		found := false
		for _, validationErr := range result.Errors {
			if validationErr.Field == "version" && validationErr.Type == "required" {
				found = true
				break
			}
		}
		assert.True(t, found, "Should have error about missing version field")
	})

	t.Run("InvalidMetric_WrongPattern", func(t *testing.T) {
		metric := &models.SberRadarMetric{
			MetricValue:       85.5,
			BaselineValue:     &[]float64{80.0}[0],
			MetricTimestamp:   time.Now().Unix(),
			MetricActualDate:  time.Now().Unix(),
			Version:           "1.0.0",
			ITServiceCI:       "INVALID123", // Wrong pattern, should be CI12345678
			ObjectCI:          &[]string{"CI87654321"}[0],
			MetricName:        "CPU_Utilization_Test",
			MetricDescription: "Тестовая метрика использования CPU",
			MetricType:        "SATURATION",
			MetricUnit:        "процент",
			MetricPeriodSec:   300,
		}

		result, err := validationService.ValidateMetric(metric)
		require.NoError(t, err)
		assert.False(t, result.Valid, "Invalid metric should fail validation")
		assert.NotEmpty(t, result.Errors, "Invalid metric should have errors")
		
		// Check that the error is about the wrong pattern
		found := false
		for _, validationErr := range result.Errors {
			if validationErr.Field == "it_service_ci" && validationErr.Type == "pattern" {
				found = true
				break
			}
		}
		assert.True(t, found, "Should have error about wrong it_service_ci pattern")
	})

	t.Run("InvalidMetric_WrongEnum", func(t *testing.T) {
		metric := &models.SberRadarMetric{
			MetricValue:       85.5,
			BaselineValue:     &[]float64{80.0}[0],
			MetricTimestamp:   time.Now().Unix(),
			MetricActualDate:  time.Now().Unix(),
			Version:           "1.0.0",
			ITServiceCI:       "CI12345678",
			ObjectCI:          &[]string{"CI87654321"}[0],
			MetricName:        "CPU_Utilization_Test",
			MetricDescription: "Тестовая метрика использования CPU",
			MetricType:        "INVALID_TYPE", // Wrong enum value
			MetricUnit:        "процент",
			MetricPeriodSec:   300,
		}

		result, err := validationService.ValidateMetric(metric)
		require.NoError(t, err)
		assert.False(t, result.Valid, "Invalid metric should fail validation")
		assert.NotEmpty(t, result.Errors, "Invalid metric should have errors")
		
		// Check that the error is about the wrong enum value
		found := false
		for _, validationErr := range result.Errors {
			if validationErr.Field == "metric_type" && validationErr.Type == "enum" {
				found = true
				break
			}
		}
		assert.True(t, found, "Should have error about wrong metric_type enum")
	})

	t.Run("ValidateMultipleMetrics", func(t *testing.T) {
		validMetric := &models.SberRadarMetric{
			MetricValue:       85.5,
			BaselineValue:     &[]float64{80.0}[0],
			MetricTimestamp:   time.Now().Unix(),
			MetricActualDate:  time.Now().Unix(),
			Version:           "1.0.0",
			ITServiceCI:       "CI12345678",
			ObjectCI:          &[]string{"CI87654321"}[0],
			MetricName:        "CPU_Utilization_Test",
			MetricDescription: "Тестовая метрика использования CPU",
			MetricType:        "SATURATION",
			MetricUnit:        "процент",
			MetricPeriodSec:   300,
		}

		invalidMetric := &models.SberRadarMetric{
			MetricValue:      85.5,
			BaselineValue:    &[]float64{80.0}[0],
			MetricTimestamp:  time.Now().Unix(),
			MetricActualDate: time.Now().Unix(),
			// Missing Version field
			ITServiceCI:       "CI12345678",
			ObjectCI:          &[]string{"CI87654321"}[0],
			MetricName:        "CPU_Utilization_Test",
			MetricDescription: "Тестовая метрика использования CPU",
			MetricType:        "SATURATION",
			MetricUnit:        "процент",
			MetricPeriodSec:   300,
		}

		metrics := []*models.SberRadarMetric{validMetric, invalidMetric}
		results, err := validationService.ValidateMetrics(metrics)
		require.NoError(t, err)
		require.Len(t, results, 2)

		assert.True(t, results[0].Valid, "First metric should be valid")
		assert.False(t, results[1].Valid, "Second metric should be invalid")

		// Test summary
		summary := validationService.GetValidationSummary(results)
		assert.Equal(t, 2, summary["total_metrics"])
		assert.Equal(t, 1, summary["valid_metrics"])
		assert.Equal(t, 1, summary["invalid_metrics"])
		assert.Greater(t, summary["total_errors"], 0)
	})

	t.Run("IsValidMetric", func(t *testing.T) {
		validMetric := &models.SberRadarMetric{
			MetricValue:       85.5,
			BaselineValue:     &[]float64{80.0}[0],
			MetricTimestamp:   time.Now().Unix(),
			MetricActualDate:  time.Now().Unix(),
			Version:           "1.0.0",
			ITServiceCI:       "CI12345678",
			ObjectCI:          &[]string{"CI87654321"}[0],
			MetricName:        "CPU_Utilization_Test",
			MetricDescription: "Тестовая метрика использования CPU",
			MetricType:        "SATURATION",
			MetricUnit:        "процент",
			MetricPeriodSec:   300,
		}

		invalidMetric := &models.SberRadarMetric{
			MetricValue:      85.5,
			BaselineValue:    &[]float64{80.0}[0],
			MetricTimestamp:  time.Now().Unix(),
			MetricActualDate: time.Now().Unix(),
			// Missing Version field
			ITServiceCI:       "CI12345678",
			ObjectCI:          &[]string{"CI87654321"}[0],
			MetricName:        "CPU_Utilization_Test",
			MetricDescription: "Тестовая метрика использования CPU",
			MetricType:        "SATURATION",
			MetricUnit:        "процент",
			MetricPeriodSec:   300,
		}

		assert.True(t, validationService.IsValidMetric(validMetric))
		assert.False(t, validationService.IsValidMetric(invalidMetric))
	})
}
