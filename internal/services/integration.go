package services

import (
	"context"
	"fmt"
	"time"

	"radar-integrator/internal/models"

	"github.com/sirupsen/logrus"
)

// IntegrationService orchestrates the integration between Zabbix and SberRadar
type IntegrationService struct {
	zabbixService    *ZabbixService
	sberRadarService *SberRadarService
	logger           *logrus.Logger
}

// NewIntegrationService creates a new integration service
func NewIntegrationService(zabbixService *ZabbixService, sberRadarService *SberRadarService, logger *logrus.Logger) *IntegrationService {
	return &IntegrationService{
		zabbixService:    zabbixService,
		sberRadarService: sberRadarService,
		logger:           logger,
	}
}

// SyncMetrics performs the complete metrics synchronization workflow
func (i *IntegrationService) SyncMetrics(ctx context.Context) (*models.SyncResult, error) {
	result := &models.SyncResult{
		Errors: make([]error, 0),
	}

	i.logger.Info("Starting metrics synchronization workflow")

	// TODO: Implement the full workflow when Zabbix integration is ready
	// For now, send dummy metrics to test SberRadar integration
	dummyMetrics := i.createDummyMetrics()

	i.logger.WithField("dummy_metrics_count", len(dummyMetrics)).Info("Created dummy metrics for testing")

	// Send dummy metrics to SberRadar
	err := i.sberRadarService.SendMetrics(ctx, dummyMetrics)
	if err != nil {
		result.ErrorCount++
		result.Errors = append(result.Errors, err)
		return result, fmt.Errorf("failed to send dummy metrics to SberRadar: %w", err)
	}

	result.MetricsSent = len(dummyMetrics)

	i.logger.WithFields(logrus.Fields{
		"metrics_sent": result.MetricsSent,
		"error_count":  result.ErrorCount,
	}).Info("Dummy metrics synchronization completed successfully")

	return result, nil
}

// createDummyMetrics creates sample metrics for testing SberRadar integration
func (i *IntegrationService) createDummyMetrics() []*models.SberRadarMetric {
	now := time.Now().Unix()

	metrics := []*models.SberRadarMetric{
		{
			// CPU utilization metric with thresholds
			MetricValue:          85.5,
			BaselineValue:        models.Float64Ptr(80.0),
			MetricTimestamp:      now,
			MetricActualDate:     now,
			Version:              "1.0.0",
			ITServiceCI:          "CI12345678",
			ObjectCI:             models.StringPtr("CI87654321"),
			MetricName:           "CPU_Utilization_Test",
			MetricDescription:    "Тестовая метрика использования CPU на сервере",
			MetricType:           "SATURATION",
			MetricUnit:           "процент",
			MetricPeriodSec:      300,
			ThresholdNormalMin:   models.Float64Ptr(0.0),
			ThresholdNormalMax:   models.Float64Ptr(70.0),
			ThresholdWarningMin:  models.Float64Ptr(70.0),
			ThresholdWarningMax:  models.Float64Ptr(85.0),
			ThresholdCriticalMin: models.Float64Ptr(85.0),
			ThresholdCriticalMax: models.Float64Ptr(100.0),
			CombinationSelector:  models.StringPtr("worst"),
			IsPercent:            models.BoolPtr(true),
			ThresholdTimestamp:   models.Int64Ptr(now),
		},
		{
			// Memory utilization metric
			MetricValue:       75.2,
			BaselineValue:     models.Float64Ptr(70.0),
			MetricTimestamp:   now,
			MetricActualDate:  now,
			Version:           "1.0.0",
			ITServiceCI:       "CI12345678",
			ObjectCI:          models.StringPtr("CI87654321"),
			MetricName:        "Memory_Utilization_Test",
			MetricDescription: "Тестовая метрика использования памяти на сервере",
			MetricType:        "SATURATION",
			MetricUnit:        "процент",
			MetricPeriodSec:   300,
			// No thresholds for this metric
		},
		{
			// Response time metric
			MetricValue:       250.0,
			BaselineValue:     models.Float64Ptr(200.0),
			MetricTimestamp:   now,
			MetricActualDate:  now,
			Version:           "1.0.0",
			ITServiceCI:       "CI12345678",
			ObjectCI:          nil, // No object CI for this metric
			MetricName:        "Response_Time_Test",
			MetricDescription: "Тестовая метрика времени отклика сервиса",
			MetricType:        "LATENCY",
			MetricUnit:        "миллисекунда",
			MetricPeriodSec:   60,
			// Baseline deviation thresholds
			BaselineNormalMin:   models.Float64Ptr(0.0),
			BaselineNormalMax:   models.Float64Ptr(50.0),
			BaselineWarningMin:  models.Float64Ptr(50.0),
			BaselineWarningMax:  models.Float64Ptr(100.0),
			BaselineCriticalMin: models.Float64Ptr(100.0),
			BaselineCriticalMax: models.Float64Ptr(200.0),
			CombinationSelector: models.StringPtr("best"),
			IsPercent:           models.BoolPtr(false),
		},
		{
			// Service availability metric
			MetricValue:          99.5,
			BaselineValue:        models.Float64Ptr(99.9),
			MetricTimestamp:      now,
			MetricActualDate:     now,
			Version:              "1.0.0",
			ITServiceCI:          "CI12345678",
			ObjectCI:             models.StringPtr("CI87654321"),
			MetricName:           "Service_Availability_Test",
			MetricDescription:    "Тестовая метрика доступности сервиса",
			MetricType:           "AVAILABILITY",
			MetricUnit:           "процент",
			MetricPeriodSec:      3600, // 1 hour
			ThresholdCriticalMin: models.Float64Ptr(95.0),
			ThresholdCriticalMax: models.Float64Ptr(100.0),
			ThresholdWarningMin:  models.Float64Ptr(98.0),
			ThresholdWarningMax:  models.Float64Ptr(100.0),
			ThresholdNormalMin:   models.Float64Ptr(99.0),
			ThresholdNormalMax:   models.Float64Ptr(100.0),
			CombinationSelector:  models.StringPtr("worst"),
			IsPercent:            models.BoolPtr(true),
			ThresholdTimestamp:   models.Int64Ptr(now),
		},
	}

	i.logger.WithField("metrics_created", len(metrics)).Info("Created dummy metrics with various threshold configurations")

	return metrics
}

// SendSingleTestMetric sends a single test metric to SberRadar for quick testing
func (i *IntegrationService) SendSingleTestMetric(ctx context.Context) error {
	now := time.Now().Unix()

	testMetric := &models.SberRadarMetric{
		MetricValue:          92.3,
		BaselineValue:        models.Float64Ptr(85.0),
		MetricTimestamp:      now,
		MetricActualDate:     now,
		Version:              "1.0.0",
		ITServiceCI:          "CI12345678",
		ObjectCI:             models.StringPtr("CI87654321"),
		MetricName:           "Single_Test_Metric",
		MetricDescription:    "Одиночная тестовая метрика для проверки SberRadar",
		MetricType:           "SATURATION",
		MetricUnit:           "процент",
		MetricPeriodSec:      300,

	}

	i.logger.Info("Sending single test metric to SberRadar")

	err := i.sberRadarService.SendMetrics(ctx, []*models.SberRadarMetric{testMetric})
	if err != nil {
		return fmt.Errorf("failed to send test metric: %w", err)
	}

	i.logger.Info("Successfully sent single test metric to SberRadar")
	return nil
}

// HealthCheck performs health checks on all integrated services
func (i *IntegrationService) HealthCheck(ctx context.Context) error {
	// Check SberRadar connectivity
	if err := i.sberRadarService.HealthCheck(ctx); err != nil {
		return fmt.Errorf("SberRadar health check failed: %w", err)
	}

	// Check Zabbix connectivity by attempting authentication
	if err := i.zabbixService.Authenticate(ctx); err != nil {
		return fmt.Errorf("Zabbix health check failed: %w", err)
	}

	return nil
}
