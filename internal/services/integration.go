package services

import (
	"context"
	"fmt"

	"radar-integrator/internal/models"

	"github.com/sirupsen/logrus"
)

// IntegrationService orchestrates the integration between Zabbix and SberRadar
type IntegrationService struct {
	zabbixService    *ZabbixService
	sberRadarService *SberRadarService
	logger           *logrus.Logger
}

// NewIntegrationService creates a new integration service
func NewIntegrationService(zabbixService *ZabbixService, sberRadarService *SberRadarService, logger *logrus.Logger) *IntegrationService {
	return &IntegrationService{
		zabbixService:    zabbixService,
		sberRadarService: sberRadarService,
		logger:           logger,
	}
}

// SyncMetrics performs the complete metrics synchronization workflow
func (i *IntegrationService) SyncMetrics(ctx context.Context) (*models.SyncResult, error) {
	result := &models.SyncResult{
		Errors: make([]error, 0),
	}

	i.logger.Info("Starting metrics synchronization workflow")

	// TODO: Implement the full workflow when Zabbix integration is ready
	// For now, this is a placeholder that returns success
	i.logger.Info("Metrics synchronization workflow completed (placeholder implementation)")

	// Placeholder: return success with no metrics sent
	result.MetricsSent = 0

	return result, nil
}

// HealthCheck performs health checks on all integrated services
func (i *IntegrationService) HealthCheck(ctx context.Context) error {
	// Check SberRadar connectivity
	if err := i.sberRadarService.HealthCheck(ctx); err != nil {
		return fmt.Errorf("SberRadar health check failed: %w", err)
	}

	// Check Zabbix connectivity by attempting authentication
	if err := i.zabbixService.Authenticate(ctx); err != nil {
		return fmt.Errorf("Zabbix health check failed: %w", err)
	}

	return nil
}
