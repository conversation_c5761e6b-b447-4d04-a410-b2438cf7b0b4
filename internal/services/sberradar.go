package services

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"radar-integrator/internal/config"
	"radar-integrator/internal/models"

	"github.com/sirupsen/logrus"
)

// SberRadarService handles SberRadar API interactions
type SberRadarService struct {
	config     config.SberRadarConfig
	logger     *logrus.Logger
	httpClient *http.Client
}

// NewSberRadarService creates a new SberRadar service
func NewSberRadarService(cfg config.SberRadarConfig, logger *logrus.Logger) *SberRadarService {
	// Load client certificate
	cert, err := tls.LoadX509KeyPair(cfg.CertPath, cfg.KeyPath)
	if err != nil {
		logger.WithError(err).Error("Failed to load client certificate")
	}

	// Configure TLS with client certificate and skip verification (like curl -k)
	tlsConfig := &tls.Config{
		Certificates:       []tls.Certificate{cert},
		MinVersion:         tls.VersionTLS12,
		InsecureSkipVerify: true, // Skip SSL certificate verification (curl -k equivalent)
	}

	// Create HTTP client with certificate authentication
	httpClient := &http.Client{
		Timeout: time.Duration(cfg.Timeout) * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: tlsConfig,
		},
	}

	return &SberRadarService{
		config:     cfg,
		logger:     logger,
		httpClient: httpClient,
	}
}

// SendMetrics sends transformed metrics to SberRadar
func (s *SberRadarService) SendMetrics(ctx context.Context, metrics []*models.SberRadarMetric) error {
	if len(metrics) == 0 {
		s.logger.Info("No metrics to send to SberRadar")
		return nil
	}

	// Send each metric individually as SberRadar expects single metric per request
	var errors []error
	successCount := 0

	for _, metric := range metrics {
		if err := s.sendSingleMetric(ctx, metric); err != nil {
			s.logger.WithError(err).WithField("metric", metric.MetricName).Error("Failed to send metric")
			errors = append(errors, err)
		} else {
			successCount++
		}
	}

	s.logger.WithFields(logrus.Fields{
		"total":   len(metrics),
		"success": successCount,
		"errors":  len(errors),
	}).Info("Metrics sending completed")

	if len(errors) > 0 {
		return fmt.Errorf("failed to send %d out of %d metrics", len(errors), len(metrics))
	}

	return nil
}

// sendSingleMetric sends a single metric to SberRadar
func (s *SberRadarService) sendSingleMetric(ctx context.Context, metric *models.SberRadarMetric) error {
	// Create payload with single metric
	payload := &models.SberRadarPayload{
		Source:    "radar-integrator",
		Timestamp: time.Now().Unix(),
		Metrics:   []*models.SberRadarMetric{metric},
	}
	return s.sendWithRetry(ctx, payload)
}

// sendWithRetry sends payload to SberRadar with retry logic
func (s *SberRadarService) sendWithRetry(ctx context.Context, payload *models.SberRadarPayload) error {
	var lastErr error

	for attempt := 0; attempt <= s.config.MaxRetries; attempt++ {
		if attempt > 0 {
			s.logger.WithFields(logrus.Fields{
				"attempt":     attempt,
				"max_retries": s.config.MaxRetries,
			}).Info("Retrying SberRadar request")

			// Wait before retry
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(time.Duration(s.config.RetryDelay) * time.Second):
			}
		}

		err := s.sendPayload(ctx, payload)
		if err == nil {
			s.logger.WithField("metrics_count", len(payload.Metrics)).Info("Successfully sent metrics to SberRadar")
			return nil
		}

		lastErr = err
		s.logger.WithError(err).WithField("attempt", attempt+1).Warn("Failed to send metrics to SberRadar")
	}

	return fmt.Errorf("failed to send metrics after %d attempts: %w", s.config.MaxRetries+1, lastErr)
}

// sendPayload sends the actual payload to SberRadar
func (s *SberRadarService) sendPayload(ctx context.Context, payload *models.SberRadarPayload) error {
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal payload: %w", err)
	}

	// Construct full URL with port
	fullURL := fmt.Sprintf("%s:%d/api/v1/metrics", s.config.BaseURL, s.config.Port)
	req, err := http.NewRequestWithContext(ctx, "POST", fullURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "radar-integrator/1.0")

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("HTTP error %d: %s", resp.StatusCode, string(body))
	}

	s.logger.WithFields(logrus.Fields{
		"status_code":   resp.StatusCode,
		"response_size": len(body),
	}).Debug("SberRadar response received")

	return nil
}

// HealthCheck performs a health check against SberRadar API
func (s *SberRadarService) HealthCheck(ctx context.Context) error {
	// Construct full URL with port
	fullURL := fmt.Sprintf("%s:%d/health", s.config.BaseURL, s.config.Port)
	req, err := http.NewRequestWithContext(ctx, "GET", fullURL, nil)
	if err != nil {
		return fmt.Errorf("failed to create health check request: %w", err)
	}

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("health check request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("health check failed with status %d", resp.StatusCode)
	}

	return nil
}
