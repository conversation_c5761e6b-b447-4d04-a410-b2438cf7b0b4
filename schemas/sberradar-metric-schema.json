{"$schema": "http://json-schema.org/draft-04/schema#", "title": "SberRadar Metric", "description": "Schema for SberRadar metric data validation", "type": "object", "properties": {"metric_value": {"description": "Число с плавающей точкой, например 7.0", "type": "number", "minimum": -4503599627370496, "maximum": 4503599627370496}, "metric_timestamp": {"type": "integer", "description": "Unix timestamp в секундах c 01/01/1970 в UTC (без timezone), обозначающий момент времени, в который метрика и baseline имели значения, указанные в полях metric_value и baseline_value соответственно", "minimum": 0, "maximum": 3147483648}, "baseline_value": {"description": "Значение базовой линии метрики, рассчитанное средствами системы мониторинга. Может не передаваться. Используется для интерпретации значения метрики в сочетании с порогами.", "anyOf": [{"type": "number", "minimum": -4503599627370496, "description": "Число с плавающей точкой, например 2.28", "maximum": 4503599627370496}, {"type": "null", "description": "... или null, если передача не производится"}]}, "metric_actual_date": {"type": "integer", "description": "Unix timestamp в секундах c 01/01/1970 в UTC (без timezone), обозначающий момент времени, в который проводилась актуализации правила сбора метрики в системе мониторинга", "minimum": 0, "maximum": 3147483648}, "version": {"type": "string", "maxLength": 16, "description": "Версия формата сообщения, определяется командой SberRadar. Для текущей версии спецификации равен \"1.0.0\""}, "it_service_ci": {"type": "string", "maxLength": 10, "pattern": "^CI[0-9]{8}$", "description": "ID КЭ ИТ-услуги, по которой собирается метрика"}, "object_ci": {"anyOf": [{"type": "string", "maxLength": 10, "pattern": "^CI[0-9]{8}$", "description": "ID КЭ объекта мониторинга"}, {"type": "null", "description": "В случае невозможности определить для объекта - передать null"}]}, "metric_name": {"type": "string", "maxLength": 255, "minLength": 3, "pattern": "^[A-Za-z0-9А-Яа-яЁё_\\-\\.\\ ]{3,255}$", "description": "Название правила сбора метрики в системе мониторинга."}, "metric_description": {"type": "string", "maxLength": 255, "minLength": 3, "pattern": "^[A-Za-z0-9А-Яа-яЁё_\\-\\.\\ ]{3,255}$", "description": "Описание метрики, характеризующее основные свойства метрики (что измеряется, о чем сигнализирует и т.п). Чем понятнее описание, тем легче определить причину отклонения и собрать правильные компетенции для его устранения."}, "metric_type": {"type": "string", "description": "Тип метрики в соответствии со Стандартом технологического выбирается из списка допустимых значений", "maxLength": 50, "enum": ["AVAILABILITY", "LATENCY", "REQ_IN_SUCCESS", "REQ_OUT_SUCCESS", "REQ_IN_ERROR", "REQ_OUT_ERROR", "SYNTHETIC", "SATURATION", "OTHER"]}, "metric_unit": {"type": "string", "maxLength": 32, "description": "Единица измерения метрики. Если применимо - рекомендуется использовать национальное кодовое обозначение единицы измерения метрики согласно Общероссийскому Классификатору Единиц Измерения (ОКЕИ, https://classifikators.ru/okei)."}, "metric_period_sec": {"type": "integer", "description": "Если применимо - интервал сбора данных метрики в целых секундах.", "minimum": 1, "maximum": 86400}}, "required": ["metric_value", "baseline_value", "metric_timestamp", "metric_actual_date", "version", "it_service_ci", "object_ci", "metric_name", "metric_description", "metric_type", "metric_unit", "metric_period_sec"], "additionalProperties": false}